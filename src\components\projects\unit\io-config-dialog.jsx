import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Settings, Plus, Edit } from "lucide-react";

const INPUT_FUNCTIONS = [
  "IP_UNUSED",
  "IP_ON_OFF",
  "BELL",
  "DO_NOT_DISTURB",
  "MAKE_UP_ROOM",
  "SCENE",
];

const OUTPUT_NAMES = [
  "MAIN_R1_OP_REM_MON",
  "MAIN_R2_CLO_REM_MOI",
  "MAIN_R3_OP_REM_DAY",
  "MAIN_R4_CLO_REM_DAY",
  "MAIN_R5_OP_REM_MON",
  "MAIN_R6_CLO_REM_MOI",
  "MAIN_R7_OP_REM_DAY",
  "MAIN_R8_CLO_REM_DAY",
  "MAIN_R9_UP_CUON_NGO",
  "MAIN_R10_DOWN_CUOI",
];

export function IOConfigDialog({
  open,
  onOpenChange,
  unitName = "RCU-21IN-10RL",
  inputCount = 11,
  outputCount = 10,
}) {
  const [inputs, setInputs] = useState([]);
  const [outputs, setOutputs] = useState([]);

  useEffect(() => {
    // Initialize inputs
    const initialInputs = [];
    for (let i = 1; i <= inputCount; i++) {
      initialInputs.push({
        id: i,
        group: i === 7 ? "Group253" : i === 8 ? "Group254" : "<Unused>",
        function:
          i === 4
            ? "BELL"
            : i === 7
            ? "DO_NOT_DISTURB"
            : i === 8
            ? "MAKE_UP_ROOM"
            : i === 11
            ? "SCENE"
            : "IP_UNUSED",
      });
    }
    setInputs(initialInputs);

    // Initialize outputs
    const initialOutputs = [];
    for (let i = 1; i <= outputCount; i++) {
      initialOutputs.push({
        id: i,
        name: OUTPUT_NAMES[i - 1] || `Output${i}`,
        group: `Group${i}`,
        acGroup: `AC ${i}`,
      });
    }
    setOutputs(initialOutputs);
  }, [inputCount, outputCount]);

  const handleInputGroupChange = (inputId, group) => {
    setInputs((prev) =>
      prev.map((input) => (input.id === inputId ? { ...input, group } : input))
    );
  };

  const handleInputFunctionChange = (inputId, func) => {
    setInputs((prev) =>
      prev.map((input) =>
        input.id === inputId ? { ...input, function: func } : input
      )
    );
  };

  const handleOutputGroupChange = (outputId, group) => {
    setOutputs((prev) =>
      prev.map((output) =>
        output.id === outputId ? { ...output, group } : output
      )
    );
  };

  const handleOutputNameChange = (outputId, name) => {
    setOutputs((prev) =>
      prev.map((output) =>
        output.id === outputId ? { ...output, name } : output
      )
    );
  };

  const handleSave = () => {
    // TODO: Implement save logic
    console.log("Saving IO configuration:", { inputs, outputs });
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Config I/O {unitName} on Network</DialogTitle>
        </DialogHeader>

        <div className="flex gap-4 h-[600px]">
          {/* Input Configuration Panel */}
          <Card className="flex-1">
            <CardHeader className="pb-3">
              <CardTitle className="text-center text-lg">
                Config Input RLC
              </CardTitle>
            </CardHeader>
            <CardContent className="overflow-y-auto max-h-[500px]">
              <div className="space-y-2">
                {inputs.map((input) => (
                  <div
                    key={input.id}
                    className="flex items-center gap-2 p-2 bg-blue-100 rounded"
                  >
                    <div className="w-16 text-sm font-medium">
                      Input {input.id}
                    </div>

                    {/* Status Icon */}
                    <div className="w-6 h-6 bg-gray-300 rounded border flex items-center justify-center">
                      <div className="w-3 h-3 bg-gray-500 rounded"></div>
                    </div>

                    <div className="flex-1">
                      <div className="text-xs text-gray-600 mb-1">Function</div>
                      <div className="flex gap-1">
                        <Select
                          value={input.group}
                          onValueChange={(value) =>
                            handleInputGroupChange(input.id, value)
                          }
                        >
                          <SelectTrigger className="h-8 text-xs">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="<Unused>">
                              &lt;Unused&gt;
                            </SelectItem>
                            <SelectItem value="Group253">Group253</SelectItem>
                            <SelectItem value="Group254">Group254</SelectItem>
                          </SelectContent>
                        </Select>

                        <Button
                          size="sm"
                          variant="outline"
                          className="h-8 w-8 p-0"
                        >
                          <Plus className="h-3 w-3" />
                        </Button>

                        <Button
                          size="sm"
                          variant="outline"
                          className="h-8 w-8 p-0"
                        >
                          <Edit className="h-3 w-3" />
                        </Button>

                        <Select
                          value={input.function}
                          onValueChange={(value) =>
                            handleInputFunctionChange(input.id, value)
                          }
                        >
                          <SelectTrigger className="h-8 text-xs flex-1">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {INPUT_FUNCTIONS.map((func) => (
                              <SelectItem key={func} value={func}>
                                {func}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>

                        <Button
                          size="sm"
                          variant="outline"
                          className="h-8 w-8 p-0"
                        >
                          <Settings className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Output Configuration Panel */}
          <Card className="flex-1">
            <CardHeader className="pb-3">
              <CardTitle className="text-center text-lg">
                Config Output RLC
              </CardTitle>
            </CardHeader>
            <CardContent className="overflow-y-auto max-h-[500px]">
              <div className="space-y-2">
                {outputs.map((output) => (
                  <div
                    key={output.id}
                    className="flex items-center gap-2 p-2 bg-blue-100 rounded"
                  >
                    <div className="w-16 text-sm font-medium">
                      Relay {output.id}
                    </div>

                    {/* Status Icons */}
                    <div className="w-6 h-6 bg-gray-300 rounded border flex items-center justify-center">
                      <div className="w-3 h-3 bg-gray-500 rounded"></div>
                    </div>

                    <div className="w-6 h-6 bg-gray-300 rounded border flex items-center justify-center">
                      <div className="w-3 h-3 bg-gray-500 rounded"></div>
                    </div>

                    <div className="flex-1">
                      <div className="flex gap-1 mb-1">
                        <Select
                          value={output.name}
                          onValueChange={(value) =>
                            handleOutputNameChange(output.id, value)
                          }
                        >
                          <SelectTrigger className="h-8 text-xs flex-1">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {OUTPUT_NAMES.map((name) => (
                              <SelectItem key={name} value={name}>
                                {name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>

                        <Button
                          size="sm"
                          variant="outline"
                          className="h-8 w-8 p-0"
                        >
                          <Plus className="h-3 w-3" />
                        </Button>

                        <Button
                          size="sm"
                          variant="outline"
                          className="h-8 w-8 p-0"
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                      </div>

                      <div className="flex gap-1">
                        <div className="flex-1">
                          <div className="text-xs text-gray-600 mb-1">
                            {output.acGroup}
                          </div>
                          <Select
                            value={output.group}
                            onValueChange={(value) =>
                              handleOutputGroupChange(output.id, value)
                            }
                          >
                            <SelectTrigger className="h-8 text-xs">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value={output.group}>
                                {output.group}
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <Button
                          size="sm"
                          variant="outline"
                          className="h-8 w-8 p-0"
                        >
                          <Settings className="h-3 w-3" />
                        </Button>

                        <Button
                          size="sm"
                          variant="outline"
                          className="h-8 w-8 p-0"
                        >
                          <Plus className="h-3 w-3" />
                        </Button>

                        <Button
                          size="sm"
                          variant="outline"
                          className="h-8 w-8 p-0"
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button variant="outline">Apply</Button>
          <Button onClick={handleSave}>OK</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
